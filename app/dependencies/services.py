from typing import Annotated

from fastapi import Depends

from services import (
    ConversationMessageService,
    ConversationService,
    DocumentService,
    ExtractedDataService,
    IntentClassifierService,
    KXDashService,
)

from .repositories import (
    ConversationMessageRepositoryDep,
    ConversationRepositoryDep,
    DocumentBlobRepositoryDep,
    DocumentDbRepositoryDep,
    DocumentQueueRepositoryDep,
    ExtractedDataRepositoryDep,
    KXDashRepositoryDep,
    OpenAIRepositoryDep,
)


__all__ = [
    'ExtractedDataServiceDep',
    'KXDashServiceDep',
    'IntentClassifierServiceDep',
    'DocumentServiceDep',
    'ConversationMessageServiceDep',
    'DocumentServiceDep',
    'KXDashServiceDep',
    'ConversationServiceDep',
]


def get_intent_classifier_service(openai_service: OpenAIRepositoryDep) -> IntentClassifierService:
    """Get the intent classifier for dependency injection."""
    return IntentClassifierService(openai_service=openai_service)


IntentClassifierServiceDep = Annotated[IntentClassifierService, Depends(get_intent_classifier_service)]


def get_extracted_data_service(
    extracted_data_repository: ExtractedDataRepositoryDep,
) -> ExtractedDataService:
    return ExtractedDataService(extracted_data_repository=extracted_data_repository)


ExtractedDataServiceDep = Annotated[ExtractedDataService, Depends(get_extracted_data_service)]


def get_kx_dash_service(
    kx_dash_repository: KXDashRepositoryDep,
    extracted_data_service: ExtractedDataServiceDep,
) -> KXDashService:
    return KXDashService(
        kx_dash_repository=kx_dash_repository,
        extracted_data_service=extracted_data_service,
    )


KXDashServiceDep = Annotated[KXDashService, Depends(get_kx_dash_service)]


def get_document_service(
    document_db_repository: DocumentDbRepositoryDep,
    document_blob_repository: DocumentBlobRepositoryDep,
    document_queue_repository: DocumentQueueRepositoryDep,
) -> DocumentService:
    return DocumentService(
        document_db_repository=document_db_repository,
        document_blob_repository=document_blob_repository,
        document_queue_repository=document_queue_repository,
    )


DocumentServiceDep = Annotated[DocumentService, Depends(get_document_service)]


def get_conversation_message_service(
    conversation_message_repository: ConversationMessageRepositoryDep,
    conversation_repository: ConversationRepositoryDep,
    document_service: DocumentServiceDep,
    kx_dash_service: KXDashServiceDep,
    intent_classifier_service: IntentClassifierServiceDep,
) -> ConversationMessageService:
    return ConversationMessageService(
        conversation_message_repository=conversation_message_repository,
        conversation_repository=conversation_repository,
        document_service=document_service,
        kx_dash_service=kx_dash_service,
        intent_classifier_service=intent_classifier_service,
    )


ConversationMessageServiceDep = Annotated[ConversationMessageService, Depends(get_conversation_message_service)]


def get_conversation_service(
    conversation_repository: ConversationRepositoryDep,
    conversation_message_service: ConversationMessageServiceDep,
    extracted_data_service: ExtractedDataServiceDep,
    document_service: DocumentServiceDep,
) -> ConversationService:
    return ConversationService(
        conversation_repository=conversation_repository,
        conversation_message_service=conversation_message_service,
        extracted_data_service=extracted_data_service,
        document_service=document_service,
    )


ConversationServiceDep = Annotated[ConversationService, Depends(get_conversation_service)]
